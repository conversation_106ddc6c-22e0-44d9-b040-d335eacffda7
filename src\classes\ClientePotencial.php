<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a potential client (Cliente Potencial).
 * Corresponds to the 'clientes_potenciales' table.
 */
class ClientePotencial
{
    // --- Attributes corresponding to DB columns ---
    private ?int    $id                  = null;
    private ?int    $aliado_comercial_id = null; // FK to aliados_comerciales
    private ?string $nombre_empresa      = null;
    private ?string $nombre_contacto     = null;
    private ?string $cargo_contacto      = null;
    private ?string $telefono_contacto   = null;
    private ?string $correo_contacto     = null;
    private ?string $ciudad              = null;
    private ?string $pais                = null;
    private ?string $fecha_registro      = null; // Handled by DB default
    private ?int    $estado              = null; // 1=Active/Pending, 0=Inactive/Closed
    private ?string $descripcion_negocio = null; // New LONGTEXT field

    /**
     * Constructor: Initializes properties with default values.
     */
    public function __construct()
    {
        $this->id                  = null;
        $this->aliado_comercial_id = null;
        $this->nombre_empresa      = null;
        $this->nombre_contacto     = null;
        $this->cargo_contacto      = null;
        $this->telefono_contacto   = null;
        $this->correo_contacto     = null;
        $this->ciudad              = null;
        $this->pais                = null;
        $this->fecha_registro      = null; // DB default
        $this->estado              = 1;    // Default to active/pending
        $this->descripcion_negocio = null; // Initialize new field
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     *
     * @param array $resultado Associative array containing property values.
     * @return self Returns a new instance of ClientePotencial.
     * @throws Exception If there's an error during construction.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                      = new self();
            $objeto->id                  = isset($resultado['id']) ? (int)$resultado['id'] : null;
            $objeto->aliado_comercial_id = isset($resultado['aliado_comercial_id']) ? (int)$resultado['aliado_comercial_id'] : null;
            $objeto->nombre_empresa      = $resultado['nombre_empresa'] ?? null;
            $objeto->nombre_contacto     = $resultado['nombre_contacto'] ?? null;
            $objeto->cargo_contacto      = $resultado['cargo_contacto'] ?? null;
            $objeto->telefono_contacto   = $resultado['telefono_contacto'] ?? null;
            $objeto->correo_contacto     = $resultado['correo_contacto'] ?? null;
            $objeto->ciudad              = $resultado['ciudad'] ?? null;
            $objeto->pais                = $resultado['pais'] ?? null;
            $objeto->fecha_registro      = $resultado['fecha_registro'] ?? null;
            $objeto->estado              = isset($resultado['estado']) ? (int)$resultado['estado'] : 1; // Default to active (1)
            $objeto->descripcion_negocio = $resultado['descripcion_negocio'] ?? null; // Assign new field

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            throw new Exception("Error constructing ClientePotencial: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a ClientePotencial object from the database by its ID.
     *
     * @param int $id       The ID of the ClientePotencial to retrieve.
     * @param PDO $conexion The database connection object.
     * @return self|null A ClientePotencial object if found, null otherwise.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        if ($id <= 0) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }

        try {
            $query = <<<SQL
            SELECT *
            FROM clientes_potenciales
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado) {
                $objeto = self::construct($resultado);
                return $objeto;
            } else {
                return null;
            }

        } catch (PDOException $e) {
            error_log("Database error getting ClientePotencial (ID: $id): " . $e->getMessage());
            throw new Exception("Database error fetching Cliente Potencial: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting ClientePotencial (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching Cliente Potencial: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all active ClientePotencial objects from the database.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of ClientePotencial objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            // Fetch only active records (estado = 1)
            $query = <<<SQL
            SELECT *
            FROM clientes_potenciales
            WHERE estado = 1
            ORDER BY nombre_empresa
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    $objeto = self::construct($resultado);
                    $listado[] = $objeto;
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting ClientePotencial list: " . $e->getMessage());
            throw new Exception("Database error fetching Cliente Potencial list: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting ClientePotencial list: " . $e->getMessage());
            throw new Exception("Error fetching Cliente Potencial list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a ClientePotencial object from the database by its aliado_comercial_id.
     *
     * @param int $aliado_comercial_id The ID of the aliado comercial to retrieve the ClientePotencial for.
     * @param PDO $conexion The database connection object.
     * @return self|null A ClientePotencial object if found, null otherwise.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getByAliadoId(int $aliado_comercial_id, PDO $conexion): ?self
    {
        if ($aliado_comercial_id <= 0) {
            throw new InvalidArgumentException("Invalid aliado_comercial_id provided.");
        }

        try {
            $query = <<<SQL
            SELECT *
            FROM clientes_potenciales
            WHERE aliado_comercial_id = :aliado_comercial_id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":aliado_comercial_id", $aliado_comercial_id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado) {
                $objeto = self::construct($resultado);
                return $objeto;
            } else {
                return null;
            }

        } catch (PDOException $e) {
            error_log("Database error getting ClientePotencial by aliado_comercial_id {$aliado_comercial_id}: " . $e->getMessage());
            throw new Exception("Database error fetching Cliente Potencial: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting ClientePotencial by aliado_comercial_id {$aliado_comercial_id}: " . $e->getMessage());
            throw new Exception("Error fetching Cliente Potencial: " . $e->getMessage());
        }
    }



    /**
     * Saves (inserts or updates) the current ClientePotencial instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        $this->validarDatos(); // Validate data before saving

        try {
            if ($this->getId() !== null && $this->getId() > 0) {
                // ID exists, perform an update
                $success = $this->_update($conexion);
            } else {
                // No ID or ID is 0/null, perform an insert
                $success = $this->_insert($conexion);
            }

            return $success;

        } catch (PDOException $e) {
            $infoParaError = $this->getNombreEmpresa() ?? $this->getCorreoContacto() ?? '[desconocido]';
            // Check for specific constraint violations if needed (e.g., unique email)
            // if ($e->getCode() == 23000 || $e->getCode() == 1062) {
            //     throw new Exception("Error saving Cliente Potencial '$infoParaError': Constraint violation.");
            // } else {
                error_log("Database error saving ClientePotencial '$infoParaError': " . $e->getMessage());
                throw new Exception("Database error saving Cliente Potencial: " . $e->getMessage());
            // }
        } catch (Exception $e) {
            error_log("General error saving ClientePotencial: " . $e->getMessage());
            throw new Exception("Error saving Cliente Potencial: " . $e->getMessage());
        }
    }

    /**
     * Saves selected services for this potential client to the clientes_potenciales_servicios table.
     * Optimized version using batch insert for better performance.
     *
     * @param PDO $conexion The PDO database connection object.
     * @param array $servicios_ids Array of service IDs to associate with this client.
     * @return bool True on success, false on failure.
     * @throws Exception If there's an error during the database operation.
     */
    public function guardarServicios(PDO $conexion, array $servicios_ids): bool
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            throw new Exception("Cliente Potencial must be saved before associating services.");
        }

        if (empty($servicios_ids)) {
            throw new Exception("At least one service must be selected.");
        }

        try {
            // First, delete existing service associations for this client
            $deleteQuery = "DELETE FROM clientes_potenciales_servicios WHERE id_cliente_potencial = :id_cliente_potencial";
            $deleteStatement = $conexion->prepare($deleteQuery);
            $deleteStatement->bindValue(':id_cliente_potencial', $this->getId(), PDO::PARAM_INT);
            $deleteStatement->execute();

            // Optimized: Use batch insert with multiple VALUES clauses
            $placeholders = [];
            $values = [];

            foreach ($servicios_ids as $index => $servicio_id) {
                $placeholders[] = "(:id_cliente_potencial_{$index}, :id_servicio_{$index})";
                $values["id_cliente_potencial_{$index}"] = $this->getId();
                $values["id_servicio_{$index}"] = (int)$servicio_id;
            }

            $insertQuery = "INSERT INTO clientes_potenciales_servicios (id_cliente_potencial, id_servicio) VALUES " . implode(', ', $placeholders);
            $insertStatement = $conexion->prepare($insertQuery);

            // Bind all values at once
            foreach ($values as $param => $value) {
                $insertStatement->bindValue(":{$param}", $value, PDO::PARAM_INT);
            }

            if (!$insertStatement->execute()) {
                throw new Exception("Failed to save service associations in batch.");
            }

            return true;

        } catch (PDOException $e) {
            error_log("Database error saving services for ClientePotencial ID {$this->getId()}: " . $e->getMessage());
            throw new Exception("Database error saving services: " . $e->getMessage());
        }
    }

    /**
     * Retrieves the services associated with this potential client.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return array Array of service IDs associated with this client.
     * @throws Exception If there's an error during the database operation.
     */
    public function getServicios(PDO $conexion): array
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            return [];
        }

        try {
            $query = <<<SQL
            SELECT id_servicio
            FROM clientes_potenciales_servicios
            WHERE id_cliente_potencial = :id_cliente_potencial
            ORDER BY id_servicio
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_cliente_potencial', $this->getId(), PDO::PARAM_INT);
            $statement->execute();

            $resultados = $statement->fetchAll(PDO::FETCH_COLUMN);
            return array_map('intval', $resultados);

        } catch (PDOException $e) {
            error_log("Database error getting services for ClientePotencial ID {$this->getId()}: " . $e->getMessage());
            throw new Exception("Database error retrieving services: " . $e->getMessage());
        }
    }



    /**
     * Inserts the current ClientePotencial instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     */
    private function _insert(PDO $conexion): bool
    {
        $query = <<<SQL
        INSERT INTO clientes_potenciales (
             aliado_comercial_id
            ,nombre_empresa
            ,nombre_contacto
            ,cargo_contacto
            ,telefono_contacto
            ,correo_contacto
            ,ciudad
            ,pais
            ,descripcion_negocio
            ,fecha_registro
        ) VALUES (
             :aliado_comercial_id
            ,:nombre_empresa
            ,:nombre_contacto
            ,:cargo_contacto
            ,:telefono_contacto
            ,:correo_contacto
            ,:ciudad
            ,:pais
            ,:descripcion_negocio
            ,:fecha_registro
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':aliado_comercial_id', $this->getAliadoComercialId(), PDO::PARAM_INT);
        $statement->bindValue(':nombre_empresa', $this->getNombreEmpresa(), PDO::PARAM_STR);
        $statement->bindValue(':nombre_contacto', $this->getNombreContacto(), PDO::PARAM_STR);
        $statement->bindValue(':cargo_contacto', $this->getCargoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':telefono_contacto', $this->getTelefonoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':correo_contacto', $this->getCorreoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':ciudad', $this->getCiudad(), PDO::PARAM_STR);
        $statement->bindValue(':pais', $this->getPais(), PDO::PARAM_STR);
        $statement->bindValue(':descripcion_negocio', $this->getDescripcionNegocio(), PDO::PARAM_STR); // Bind new field
        $statement->bindValue(':fecha_registro', create_datetime());

        $success = $statement->execute();

        if ($success) {
            $this->setId((int)$conexion->lastInsertId());
            // Fetch and set fecha_registro if needed immediately after insert
            // $this->fecha_registro = self::get($this->id, $conexion)?->getFechaRegistro();
        } else {
            error_log("Failed to insert ClientePotencial: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current ClientePotencial instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     */
    private function _update(PDO $conexion): bool
    {
        $query = <<<SQL
        UPDATE clientes_potenciales SET
             aliado_comercial_id = :aliado_comercial_id
            ,nombre_empresa = :nombre_empresa
            ,nombre_contacto = :nombre_contacto
            ,cargo_contacto = :cargo_contacto
            ,telefono_contacto = :telefono_contacto
            ,correo_contacto = :correo_contacto
            ,ciudad = :ciudad
            ,pais = :pais
            ,estado = :estado
            ,descripcion_negocio = :descripcion_negocio
            -- fecha_registro is usually not updated
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':aliado_comercial_id', $this->getAliadoComercialId(), PDO::PARAM_INT);
        $statement->bindValue(':nombre_empresa', $this->getNombreEmpresa(), PDO::PARAM_STR);
        $statement->bindValue(':nombre_contacto', $this->getNombreContacto(), PDO::PARAM_STR);
        $statement->bindValue(':cargo_contacto', $this->getCargoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':telefono_contacto', $this->getTelefonoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':correo_contacto', $this->getCorreoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':ciudad', $this->getCiudad(), PDO::PARAM_STR);
        $statement->bindValue(':pais', $this->getPais(), PDO::PARAM_STR);
        $statement->bindValue(':estado', $this->getEstado(), PDO::PARAM_INT);
        $statement->bindValue(':descripcion_negocio', $this->getDescripcionNegocio(), PDO::PARAM_STR); // Bind new field
        $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update ClientePotencial (ID: {$this->getId()}): " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deactivates a ClientePotencial record in the database by setting its estado to 0.
     *
     * @param int $id       The ID of the ClientePotencial to deactivate.
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If the ID is invalid or a database error occurs.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        if ($id <= 0) {
            throw new InvalidArgumentException("Invalid ID provided for deactivation.");
        }

        try {
            $query = "UPDATE clientes_potenciales SET estado = 0 WHERE id = :id";
            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            return $statement->execute();
        } catch (PDOException $e) {
            error_log("Database error deactivating ClientePotencial (ID: $id): " . $e->getMessage());
            throw new Exception("Database error deactivating Cliente Potencial: " . $e->getMessage());
        }
    }


    /**
     * Validates the essential data for the ClientePotencial.
     * Throws an Exception if validation fails.
     *
     * @throws Exception If validation fails.
     */
    private function validarDatos(): void
    {
        if (empty($this->getAliadoComercialId()) || $this->getAliadoComercialId() <= 0) {
            throw new Exception("El ID del aliado comercial es requerido.");
        }
        if (empty(trim((string)$this->getNombreEmpresa()))) {
            throw new Exception("El nombre de la empresa es requerido.");
        }
        if (empty(trim((string)$this->getNombreContacto()))) {
            throw new Exception("El nombre del contacto es requerido.");
        }
        if (empty(trim((string)$this->getTelefonoContacto()))) {
            throw new Exception("El teléfono de contacto es requerido.");
        }
        if (empty(trim((string)$this->getCorreoContacto()))) {
            throw new Exception("El correo electrónico de contacto es requerido.");
        }
        if (!filter_var($this->getCorreoContacto(), FILTER_VALIDATE_EMAIL)) {
            throw new Exception("El formato del correo electrónico de contacto no es válido.");
        }
        if (empty(trim((string)$this->getCiudad()))) {
            throw new Exception("La ciudad es requerida.");
        }
        if (empty(trim((string)$this->getPais()))) {
            throw new Exception("El país es requerido.");
        }
        // cargo_contacto is optional
    }


    // --- GETTERS AND SETTERS ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getAliadoComercialId(): ?int
    {
        return $this->aliado_comercial_id;
    }

    public function setAliadoComercialId(?int $aliado_comercial_id): self
    {
        $this->aliado_comercial_id = $aliado_comercial_id;
        return $this;
    }

    public function getNombreEmpresa(): ?string
    {
        return $this->nombre_empresa;
    }

    public function setNombreEmpresa(?string $nombre_empresa): self
    {
        $this->nombre_empresa = $nombre_empresa;
        return $this;
    }

    public function getNombreContacto(): ?string
    {
        return $this->nombre_contacto;
    }

    public function setNombreContacto(?string $nombre_contacto): self
    {
        $this->nombre_contacto = $nombre_contacto;
        return $this;
    }

    public function getCargoContacto(): ?string
    {
        return $this->cargo_contacto;
    }

    public function setCargoContacto(?string $cargo_contacto): self
    {
        $this->cargo_contacto = $cargo_contacto;
        return $this;
    }

    public function getTelefonoContacto(): ?string
    {
        return $this->telefono_contacto;
    }

    public function setTelefonoContacto(?string $telefono_contacto): self
    {
        $this->telefono_contacto = $telefono_contacto;
        return $this;
    }

    public function getCorreoContacto(): ?string
    {
        return $this->correo_contacto;
    }

    public function setCorreoContacto(?string $correo_contacto): self
    {
        $this->correo_contacto = $correo_contacto;
        return $this;
    }

    public function getCiudad(): ?string
    {
        return $this->ciudad;
    }

    public function setCiudad(?string $ciudad): self
    {
        $this->ciudad = $ciudad;
        return $this;
    }

    public function getPais(): ?string
    {
        return $this->pais;
    }

    public function setPais(?string $pais): self
    {
        $this->pais = $pais;
        return $this;
    }

    public function getFechaRegistro(): ?string
    {
        return $this->fecha_registro;
    }

    /**
     * Note: Setting fecha_registro might not be typical as it's often handled by the database.
     */
    public function setFechaRegistro(?string $fecha_registro): self
    {
        $this->fecha_registro = $fecha_registro;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        // Optional: Add validation if needed (e.g., ensure estado is 0 or 1)
        if ($estado !== 0 && $estado !== 1) {
             throw new InvalidArgumentException("Estado must be 0 or 1.");
        }
        $this->estado = $estado;
        return $this;
    }

    public function getDescripcionNegocio(): ?string
    {
        return $this->descripcion_negocio;
    }

    public function setDescripcionNegocio(?string $descripcion_negocio): self
    {
        $this->descripcion_negocio = $descripcion_negocio;
        return $this;
    }



}
