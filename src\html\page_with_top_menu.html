@@include('./partial/head.html', {
	"title": "Page with Top Menu", 
	"css": [
	]}
)
	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu@@if(context.theme == 'material' || context.theme == 'google') { app-with-wide-sidebar}@@if(context.theme == 'google') { app-with-light-sidebar}">
		@@include('./partial/header.html', {"pageWithTopMenu": "true", "pageWithoutSidebar": "true"})
		@@include('./partial/top-menu.html', {"pageOptionsClass": "active", "pageWithTopMenuClass": "active"})
		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN breadcrumb -->
			<ol class="breadcrumb@@if(context.theme != 'facebook'){ float-xl-end}">
				<li class="breadcrumb-item"><a href="javascript:;">Home</a></li>
				<li class="breadcrumb-item"><a href="javascript:;">Page Options</a></li>
				<li class="breadcrumb-item active">Page with Top Menu</li>
			</ol>
			<!-- END breadcrumb -->
			<!-- BEGIN page-header -->
			<h1 class="page-header">Page with Top Menu <small>header small text goes here...</small></h1>
			<!-- END page-header -->
			
			<!-- BEGIN panel -->
			<div class="panel panel-inverse">
				<div class="panel-heading">
					<h4 class="panel-title">Installation Settings</h4>
					<div class="panel-heading-btn">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
						<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
					</div>
				</div>
				<div class="panel-body">
					<p>
						Add the <code>.app-with-top-menu</code> css class to <code>.app</code> container for top menu page setting. Besides that you might also need to add the <b>top menu element</b> to the page as well.
					</p>
				</div>
				<div class="hljs-wrapper">
					<pre><code class="html">&lt;div id="app" class="app app-with-top-menu"&gt;
  &lt;div id="header" class="app-header"&gt;
    &lt;div class="navbar-header"&gt;
      &lt;a href="index.html" class="navbar-brand"&gt;...&lt;/a&gt;
      &lt;button type="button" class="navbar-mobile-toggler" data-toggle="app-top-menu-mobile"&gt;
        &lt;span class="icon-bar"&gt;&lt;/span&gt;
        &lt;span class="icon-bar"&gt;&lt;/span&gt;
        &lt;span class="icon-bar"&gt;&lt;/span&gt;
      &lt;/button&gt;
    &lt;/div&gt;
    ...
  &lt;/div&gt;
  
  &lt;div id="top-menu" class="app-top-menu"&gt;
    &lt;div class="menu"&gt;
      &lt;div class="menu-item has-sub"&gt;
        &lt;a href="" class="menu-link"&gt;
          &lt;div class="menu-icon"&gt;
            &lt;i class="fa fa-align-left"&gt;&lt;/i&gt;
          &lt;/div&gt;
          &lt;div class="menu-text"&gt;Menu Level&lt;/div&gt;
          &lt;div class="menu-caret"&gt;&lt;/div&gt;
        &lt;/a&gt;
        &lt;div class="menu-submenu"&gt;
          &lt;div class="menu-item"&gt;
            &lt;a href="" class="menu-link"&gt;
              &lt;div class="menu-text"&gt;Menu 1.1&lt;/div&gt;
              &lt;div class="menu-caret"&gt;&lt;/div&gt;
            &lt;/a&gt;
          &lt;/div&gt;
          ...
        &lt;/div&gt;
      &lt;/div&gt;
      ...
      &lt;div class="menu-item menu-control menu-control-start"&gt;
        &lt;a href="javascript:;" class="menu-link" data-toggle="app-top-menu-prev"&gt;&lt;i class="fa fa-angle-left"&gt;&lt;/i&gt;&lt;/a&gt;
      &lt;/div&gt;
      &lt;div class="menu-item menu-control menu-control-end"&gt;
        &lt;a href="javascript:;" class="menu-link" data-toggle="app-top-menu-next"&gt;&lt;i class="fa fa-angle-right"&gt;&lt;/i&gt;&lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;</code></pre>
				</div>
			</div>
			<!-- END panel -->
		</div>
		<!-- END #content -->
		
		@@include('./partial/theme-panel.html')
		@@include('./partial/scroll-top-btn.html')
	</div>
	<!-- END #app -->
	
@@include('./partial/script.html', {
	"script": [
		"../assets/plugins/@highlightjs/cdn-assets/highlight.min.js",
		"../assets/js/demo/render.highlight.js"
	]}
)