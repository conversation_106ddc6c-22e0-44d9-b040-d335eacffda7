@@include('./partial/head.html', {
	"title": "Basic Tables", 
	"css": [
	]}
)
	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed@@if(context.theme == 'material' || context.theme == 'google') { app-with-wide-sidebar}@@if(context.theme == 'google') { app-with-light-sidebar}">
		@@include('./partial/header.html')
		@@include('./partial/sidebar.html', {"tablesClass": "active", "tableBasicClass": "active"})
		
		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN breadcrumb -->
			<ol class="breadcrumb@@if(context.theme != 'facebook'){ float-xl-end}">
				<li class="breadcrumb-item"><a href="javascript:;">Home</a></li>
				<li class="breadcrumb-item"><a href="javascript:;">Tables</a></li>
				<li class="breadcrumb-item active">Basic Tables</li>
			</ol>
			<!-- END breadcrumb -->
			<!-- BEGIN page-header -->
			<h1 class="page-header">Basic Tables <small>header small text goes here...</small></h1>
			<!-- END page-header -->
			<!-- BEGIN row -->
			<div class="row">
				<!-- BEGIN col-6 -->
				<div class="col-xl-6">
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-1">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Default Table</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table mb-0">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>2</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>3</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table"&gt;
  ...
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-2">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Hover Table</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table table-hover mb-0 @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white}">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>2</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>3</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table table-hover"&gt;
  ...
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-3">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Table Small</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table table-sm mb-0 @@if(context.theme != 'transparent'){text-dark}@@if(context.theme == 'transparent'){text-white}">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>2</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>3</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table table-sm"&gt;
  ...
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-4">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Responsive Table</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table">
									<thead>
										<tr>
											<th>#</th>
											<th nowrap>Table heading</th>
											<th nowrap>Table heading</th>
											<th nowrap>Table heading</th>
											<th nowrap>Table heading</th>
											<th nowrap>Table heading</th>
											<th nowrap>Table heading</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
										</tr>
										<tr>
											<td>2</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
										</tr>
										<tr>
											<td>3</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
											<td>Table cell</td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;div class="table-responsive"&gt;
  &lt;table class="table"&gt;
    ...
  &lt;/table&gt;
&lt;/div&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-5">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Table Striped</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table table-striped mb-0">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>2</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>3</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table table-striped"&gt;
  ...
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-6">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Bordered Table</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table table-bordered mb-0">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>2</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>3</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table table-bordered"&gt;
  ...
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
				</div>
				<!-- END col-6 -->
				<!-- BEGIN col-6 -->
				<div class="col-xl-6">
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-7">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">UI Elements in Table <span class="badge bg-success ms-5px">NEW</span></h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table table-striped mb-0 align-middle">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
											<th width="1%"></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<img src="../assets/img/user/user-1.jpg" class="rounded h-30px" />
											</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
											<td nowrap>
												<a href="#" class="btn btn-sm btn-primary w-60px me-1">Edit</a>
												<a href="#" class="btn btn-sm btn-white w-60px">Delete</a>
											</td>
										</tr>
										<tr>
											<td>
												<img src="../assets/img/user/user-2.jpg" class="rounded h-30px" />
											</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
											<td nowrap>
												<div class="btn-group">
													<a href="#" class="btn btn-white btn-sm w-90px">Settings</a>
													<a href="#" class="btn btn-white btn-sm dropdown-toggle w-30px no-caret" data-bs-toggle="dropdown">
													<span class="caret"></span>
													</a>
													<div class="dropdown-menu dropdown-menu-end">
														<a href="#" class="dropdown-item">Action 1</a>
														<a href="#" class="dropdown-item">Action 2</a>
														<a href="#" class="dropdown-item">Action 3</a>
														<div class="dropdown-divider"></div>
														<a href="#" class="dropdown-item">Action 4</a>
													</div>
												</div>
											</td>
										</tr>
										<tr>
											<td>
												<img src="../assets/img/user/user-3.jpg" class="rounded h-30px" />
											</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
											<td class="with-btn" nowrap>
												<a href="#" class="btn btn-sm btn-primary w-60px me-1">Edit</a>
												<a href="#" class="btn btn-sm btn-white w-60px">Delete</a>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table align-middle"&gt;
  ...
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-8">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Form Elements in Table <span class="badge bg-success ms-1">NEW</span></h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table table-striped align-middle mb-0">
									<thead>
										<tr>
											<th>
												<div class="form-check">
													<input type="checkbox" value="" id="table_checkbox_1" class="form-check-input" />
													<label for="table_checkbox_1" class="form-check-label">&nbsp;</label>
												</div>
											</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>
												<div class="form-check">
													<input type="checkbox" value="" id="table_checkbox_2" class="form-check-input" />
													<label for="table_checkbox_2" class="form-check-label">&nbsp;</label>
												</div>
											</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>
												<div class="form-check">
													<input type="radio" value="" id="table_radio_1" class="form-check-input" />
													<label for="table_radio_1" class="form-check-label">&nbsp;</label>
												</div>
											</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>
												<div class="form-check">
													<input type="radio" value="" id="table_radio_2" class="form-check-input" />
													<label for="table_radio_2" class="form-check-label">&nbsp;</label>
												</div>
											</td>
											<td><input type="text" class="form-control my-n1" value="Harvinder Singh" /></td>
											<td><EMAIL></td>
										</tr>
										<tr>
											<td>
												<div class="form-check">
													<input type="radio" value="" id="table_radio_3" class="form-check-input" />
													<label for="table_radio_3" class="form-check-label">&nbsp;</label>
												</div>
											</td>
											<td>
												<div class="input-group my-n1">
													<div class="input-group-text">@</div>
													<input type="text" class="form-control" placeholder="Terry" />
												</div>
											</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table align-middle"&gt;
  &lt;tbody&gt;
    &lt;tr&gt;
      &lt;!-- with input --&gt;
      &lt;td&gt;
        &lt;input type="text" class="form-control my-n1" /&gt;
      &lt;/td&gt;
    &lt;/tr&gt;
    
    &lt;tr&gt;
      &lt;!-- with input-group --&gt;
      &lt;td&gt;
        &lt;div class="input-group my-n1"&gt;&lt;/div&gt;
      &lt;/td&gt;
    &lt;/tr&gt;
    
    &lt;tr&gt;
      &lt;!-- with btn-group --&gt;
      &lt;td&gt;
        &lt;div class="btn-group my-n1"&gt;&lt;/div&gt;
      &lt;/td&gt;
    &lt;/tr&gt;
  &lt;/tbody&gt;
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="table-basic-9">
						<!-- BEGIN panel-heading -->
						<div class="panel-heading">
							<h4 class="panel-title">Table Row Classes</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<!-- END panel-heading -->
						<!-- BEGIN panel-body -->
						<div class="panel-body">
							<!-- BEGIN table-responsive -->
							<div class="table-responsive">
								<table class="table mb-0">
									<thead>
										<tr>
											<th>#</th>
											<th>Username</th>
											<th>Email Address</th>
										</tr>
									</thead>
									<tbody>
										<tr class="table-active">
											<td>1</td>
											<td>Nicky Almera</td>
											<td><EMAIL></td>
										</tr>
										<tr class="table-info">
											<td>2</td>
											<td>Terry Khoo</td>
											<td><EMAIL></td>
										</tr>
										<tr class="table-success">
											<td>3</td>
											<td>Edmund Wong</td>
											<td><EMAIL></td>
										</tr>
										<tr class="table-warning">
											<td>4</td>
											<td>Harvinder Singh</td>
											<td><EMAIL></td>
										</tr>
										<tr class="table-danger">
											<td>5</td>
											<td>Terry Khoo</td>
											<td><EMAIL></td>
										</tr>
									</tbody>
								</table>
							</div>
							<!-- END table-responsive -->
						</div>
						<!-- END panel-body -->
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;table class="table"&gt;
  &lt;tbody&gt;
    &lt;tr class="table-active"&gt;...&lt;/tr&gt;
    &lt;tr class="table-info"&gt;...&lt;/tr&gt;
    &lt;tr class="table-success"&gt;...&lt;/tr&gt;
    &lt;tr class="table-warning"&gt;...&lt;/tr&gt;
    &lt;tr class="table-danger"&gt;...&lt;/tr&gt;
  &lt;/tbody&gt;
&lt;/table&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
				</div>
				<!-- END col-6 -->
			</div>
			<!-- END row -->
		</div>
		<!-- END #content -->
		
		@@include('./partial/theme-panel.html')
		@@include('./partial/scroll-top-btn.html')
	</div>
	<!-- END #app -->
	
@@include('./partial/script.html', {
	"script": [
		"../assets/plugins/@highlightjs/cdn-assets/highlight.min.js",
		"../assets/js/demo/render.highlight.js"
	]}
)
