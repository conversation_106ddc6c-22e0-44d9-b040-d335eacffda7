<?php

declare(strict_types=1);

namespace App\classes;

use App\classes\FreelancerEspecializacion;
use App\classes\Especializacion;
use App\classes\FreelancerTarifa;
use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/** @var FreelancerEspecializacion[] */
class Freelancer
{
	private ?int                      $id                        = null;
	private ?string                   $nombre_completo           = null;
	private ?string                   $tipo_documento            = null;
	private ?string                   $documento_identidad       = null;
	private ?string                   $correo_electronico        = null;
	private ?string                   $numero_telefono           = null;
	private ?string                   $direccion_completa        = null;
	private ?string                   $ciudad_residencia         = null;
	private ?string                   $pais_residencia           = null;
	private ?string                   $fecha_creacion            = null;
	private ?int                      $leyo_consentimiento       = 0;
	private ?string                   $fecha_leyo_consentimiento = null;
	private ?int                      $estado                    = 1;
	private ?FreelancerExperiencia    $experiencia               = null;
	private ?FreelancerDisponibilidad $disponibilidad            = null;
	private array                     $especializaciones         = [];
	private ?FreelancerReferencia     $referencias               = null;
	private ?FreelancerTarifa         $tarifa                    = null;
	private ?FreelancerDocumento      $documentos                = null;
	
	public function __construct()
	{
		$this->id                        = 0;
		$this->nombre_completo           = null;
		$this->tipo_documento            = null;
		$this->documento_identidad       = null;
		$this->correo_electronico        = null;
		$this->numero_telefono           = null;
		$this->direccion_completa        = null;
		$this->ciudad_residencia         = null;
		$this->pais_residencia           = null;
		$this->fecha_creacion            = null;
		$this->leyo_consentimiento       = 0;
		$this->fecha_leyo_consentimiento = null;
		$this->estado                    = 1;
		$this->especializaciones         = [];
		$this->experiencia               = null;
		$this->disponibilidad            = null;
		$this->referencias               = null;
		$this->tarifa                    = null;
		$this->documentos                = null;
	}
	
	/**
	 * @throws Exception
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                            = new self();
			$objeto->id                        = $resultado['id'] ?? 0;
			$objeto->nombre_completo           = $resultado['nombre_completo'] ?? null;
			$objeto->tipo_documento            = $resultado['tipo_documento'] ?? null;
			$objeto->documento_identidad       = $resultado['documento_identidad'] ?? null;
			$objeto->correo_electronico        = $resultado['correo_electronico'] ?? null;
			$objeto->numero_telefono           = $resultado['numero_telefono'] ?? null;
			$objeto->direccion_completa        = $resultado['direccion_completa'] ?? null;
			$objeto->ciudad_residencia         = $resultado['ciudad_residencia'] ?? null;
			$objeto->pais_residencia           = $resultado['pais_residencia'] ?? null;
			$objeto->fecha_creacion            = $resultado['fecha_creacion'] ?? null;
			$objeto->leyo_consentimiento       = $resultado['leyo_consentimiento'] ?? 0;
			$objeto->fecha_leyo_consentimiento = $resultado['fecha_leyo_consentimiento'] ?? null;
			$objeto->estado                    = $resultado['estado'] ?? 1;
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception("Error al construir Freelancers: " . $e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM freelancers
            WHERE
            	id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			if ($resultado) {
				$freelancer = self::construct($resultado);
				
				return $freelancer;
			} else {
				return null; // Return null for not found
			}
		} catch (Exception $e) {
			throw new Exception("Error al obtener Freelancers: " . $e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM freelancers
            WHERE
            	estado = 1
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			if (!$resultados) {
				return [];
			} else {
				$listado = [];
				
				foreach ($resultados as $resultado) {
					$freelancer = self::construct($resultado);
					
					$listado[] = $freelancer;
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception("Error al obtener lista de Freelancers: " . $e->getMessage());
		}
	}
	
	// --- Métodos Estáticos para Acceso a Datos ---
	
	/**
	 * Crea este freelancer (instancia actual) en la base de datos.
	 * Las propiedades del objeto deben estar seteadas antes de llamar a este método.
	 * Actualiza el ID del objeto tras la inserción.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function guardar(PDO $conexion): void
	{
		// Validaciones básicas sobre el objeto actual ($this)
		if (empty($this->getNombreCompleto()) || empty($this->getTipoDocumento()) || empty($this->getDocumentoIdentidad()) || empty($this->getCorreoElectronico()) || empty($this->getNumeroTelefono())) {
			throw new Exception("Los campos nombre, tipo/número de documento, correo y teléfono son requeridos para crear un freelancer.");
		}
		// Añadir más validaciones si es necesario
		if (!empty($this->getCorreoElectronico()) && !filter_var($this->getCorreoElectronico(), FILTER_VALIDATE_EMAIL)) {
			throw new Exception("El formato del correo electrónico no es válido.");
		}
		
		try {
			// Establecer fecha de consentimiento si se leyó y no está ya seteada en el objeto
			$fechaConsentimiento = ($this->getLeyoConsentimiento() === 1)
				? ($this->getFechaLeyeConsentimiento() ?? date('Y-m-d H:i:s')) // Usar fecha existente o actual
				: null;
			// Actualizar la fecha en el objeto por si acaso se generó ahora
			if ($this->getLeyoConsentimiento() === 1 && $this->getFechaLeyeConsentimiento() === null) {
				$this->setFechaLeyeConsentimiento($fechaConsentimiento);
			}

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
             INSERT INTO freelancers (
                  nombre_completo
                 ,tipo_documento
                 ,documento_identidad
                 ,correo_electronico
                 ,numero_telefono
                 ,direccion_completa
                 ,ciudad_residencia
                 ,pais_residencia
                 ,fecha_creacion
                 ,leyo_consentimiento
                 ,fecha_leyo_consentimiento
             ) VALUES (
                  :nombre_completo
                 ,:tipo_documento
                 ,:documento_identidad
                 ,:correo_electronico
                 ,:numero_telefono
                 ,:direccion_completa
                 ,:ciudad_residencia
                 ,:pais_residencia
                 ,:fecha_creacion
                 ,:leyo_consentimiento
                 ,:fecha_leyo_consentimiento
             )
             SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre_completo', $this->getNombreCompleto(), PDO::PARAM_STR);
			$statement->bindValue(':tipo_documento', $this->getTipoDocumento(), PDO::PARAM_STR);
			$statement->bindValue(':documento_identidad', $this->getDocumentoIdentidad(), PDO::PARAM_STR);
			$statement->bindValue(':correo_electronico', $this->getCorreoElectronico(), PDO::PARAM_STR);
			$statement->bindValue(':numero_telefono', $this->getNumeroTelefono(), PDO::PARAM_STR);
			$statement->bindValue(':direccion_completa', $this->getDireccionCompleta(), PDO::PARAM_STR);
			$statement->bindValue(':ciudad_residencia', $this->getCiudadResidencia(), PDO::PARAM_STR);
			$statement->bindValue(':pais_residencia', $this->getPaisResidencia(), PDO::PARAM_STR);
			$statement->bindValue(':fecha_creacion', create_datetime());
			$statement->bindValue(':leyo_consentimiento', $this->getLeyoConsentimiento() ?? 0, PDO::PARAM_INT);
			$statement->bindValue(':fecha_leyo_consentimiento', $fechaConsentimiento, PDO::PARAM_STR); // Usar la variable calculada/verificada
			
			// Ejecutar la consulta
			$success = $statement->execute();
			
			if ($success) {
				// Obtener el ID y actualizar el objeto actual
				$this->setId((int)$conexion->lastInsertId());
				
				// -- Guardar Experiencia --
				$this->experiencia->setFreelancer($this);
				if (!$this->experiencia->guardar($conexion)) {
					throw new Exception("No se pudo guardar la información de experiencia del freelancer.");
				}
				// -- Guardar Disponibilidad --
				$this->disponibilidad->setFreelancer($this);
				if (!$this->disponibilidad->guardar($conexion)) {
					throw new Exception("No se pudo guardar la información de disponibilidad del freelancer.");
				}
				// -- Guardar las referencias (insertará o actualizará la fila única para este freelancer) --
				$this->referencias->setFreelancer($this);
				if (!$this->referencias->guardar($conexion)) {
					throw new Exception("No se pudo guardar la información de referencias del freelancer (ID: {$this->getId()}).");
				}
				// -- Guardar tarifa --
				$this->tarifa->setFreelancer($this);
				if (!$this->tarifa->guardar($conexion)) {
					throw new Exception("No se pudo guardar la información de tarifa.");
				}
			} else {
				error_log("Error al ejecutar INSERT para freelancer: " . implode(" | ", $statement->errorInfo()));
				$this->setId(0);
			}
		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. duplicados)
			$infoParaError = $this->getNombreCompleto() ?? $this->getCorreoElectronico() ?? $this->getDocumentoIdentidad() ?? '[desconocido]';
			if ($e->getCode() == 23000 || $e->getCode() == 1062) {
				throw new Exception("Error al crear freelancer '$infoParaError': El documento o correo electrónico ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear freelancer '$infoParaError': " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error general al crear freelancer: " . $e->getMessage());
		}
	}
	
	/**
	 * Guarda las especializaciones asociadas a este freelancer en la tabla de enlace.
	 * Borra las existentes y luego inserta las nuevas del array $this->especializaciones.
	 * Requiere que el ID del freelancer esté establecido.
	 *
	 * @param PDO $conexion
	 *
	 * @return bool
	 * @throws Exception
	 */
	public function guardarEspecializaciones(PDO $conexion): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("Se requiere un ID de Freelancer válido para guardar especializaciones.");
		}
		
		try {
			// 1. Borrar todas las especializaciones existentes para este freelancer
			FreelancerEspecializacion::deleteByFreelancerId($this->getId(), $conexion);
			
			// 2. Guardar (insertar) las nuevas especializaciones desde el array de objetos
			if (!empty($this->especializaciones)) {
				foreach ($this->especializaciones as $link) {
					if ($link instanceof FreelancerEspecializacion) {
						// Asegurarse que el link esté asociado a ESTE freelancer
						$link->setFreelancer($this);
						
						if (!$link->guardar($conexion)) {
							error_log("Error al guardar enlace especialización para Freelancer ID: " . $this->getId() . ", Especializacion ID: " . $link->getEspecializacion()?->getId());
							throw new Exception("Error al guardar una de las especializaciones.");
						}
					} else {
						error_log("Se encontró un elemento no válido en el array de especializaciones para Freelancer ID: " . $this->getId());
					}
				}
			}
			return true; // Éxito general si no hubo excepciones fatales
		} catch (Exception $e) {
			// Relanzar o manejar el error
			throw new Exception("Error de base de datos al guardar especializaciones del freelancer (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Saves the services associated with this freelancer in the freelancers_servicios table.
	 * Deletes existing associations and inserts new ones.
	 * Requires that the freelancer ID is set.
	 *
	 * @param array $servicios_ids Array of service IDs to associate with this freelancer.
	 * @param PDO $conexion The PDO database connection object.
	 * @return bool True on success, false on failure.
	 * @throws Exception If the freelancer ID is invalid or there's a database error.
	 */
	public function guardarServicios(array $servicios_ids, PDO $conexion): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("Se requiere un ID de Freelancer válido para guardar servicios.");
		}

		if (empty($servicios_ids)) {
			throw new Exception("Debe seleccionar al menos un servicio.");
		}

		try {
			// First, delete existing service associations for this freelancer
			$deleteQuery = "DELETE FROM freelancers_servicios WHERE id_freelancer = :id_freelancer";
			$deleteStatement = $conexion->prepare($deleteQuery);
			$deleteStatement->bindValue(':id_freelancer', $this->getId(), PDO::PARAM_INT);
			$deleteStatement->execute();

			// Optimized: Use batch insert with multiple VALUES clauses
			$placeholders = [];
			$values = [];

			foreach ($servicios_ids as $index => $servicio_id) {
				$placeholders[] = "(:id_freelancer_{$index}, :id_servicio_{$index})";
				$values["id_freelancer_{$index}"] = $this->getId();
				$values["id_servicio_{$index}"] = (int)$servicio_id;
			}

			$insertQuery = "INSERT INTO freelancers_servicios (id_freelancer, id_servicio) VALUES " . implode(', ', $placeholders);
			$insertStatement = $conexion->prepare($insertQuery);

			// Bind all values at once
			foreach ($values as $param => $value) {
				$insertStatement->bindValue(":{$param}", $value, PDO::PARAM_INT);
			}

			if (!$insertStatement->execute()) {
				throw new Exception("Failed to save service associations in batch.");
			}

			return true;

		} catch (PDOException $e) {
			error_log("Database error saving services for Freelancer ID {$this->getId()}: " . $e->getMessage());
			throw new Exception("Database error saving services: " . $e->getMessage());
		}
	}

	/**
	 * Retrieves the services associated with this freelancer.
	 *
	 * @param PDO $conexion The PDO database connection object.
	 * @return array Array of service IDs associated with this freelancer.
	 * @throws Exception If there's an error during the database operation.
	 */
	public function getServicios(PDO $conexion): array
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			return [];
		}

		try {
			$query = "SELECT id_servicio FROM freelancers_servicios WHERE id_freelancer = :id_freelancer";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_freelancer', $this->getId(), PDO::PARAM_INT);
			$statement->execute();

			$servicios_ids = [];
			while ($row = $statement->fetch(PDO::FETCH_ASSOC)) {
				$servicios_ids[] = (int)$row['id_servicio'];
			}

			return $servicios_ids;

		} catch (PDOException $e) {
			error_log("Database error getting services for Freelancer ID {$this->getId()}: " . $e->getMessage());
			throw new Exception("Database error retrieving services: " . $e->getMessage());
		}
	}

	/**
	 * Retrieves the services with their categories for display purposes.
	 *
	 * @param PDO $conexion The PDO database connection object.
	 * @return array Array of arrays containing service and category information.
	 * @throws Exception If there's an error during the database operation.
	 */
	public function getServiciosConCategorias(PDO $conexion): array
	{
		$servicios_ids = $this->getServicios($conexion);
		$servicios_con_categorias = [];

		if (empty($servicios_ids)) {
			return [];
		}

		foreach ($servicios_ids as $servicio_id) {
			try {
				$servicio = Servicio::get($servicio_id, $conexion);
				if ($servicio) {
					$categoria = ServicioCategoria::get($servicio->getId_servicio_categoria(), $conexion);
					$servicios_con_categorias[] = [
						'servicio' => $servicio,
						'categoria' => $categoria
					];
				}
			} catch (Exception $e) {
				// Log error but continue with other services
				error_log("Error getting service or category for service ID {$servicio_id}: " . $e->getMessage());
				// Optionally, you could still add the service with null category
				// $servicios_con_categorias[] = ['servicio' => $servicio, 'categoria' => null];
			}
		}

		return $servicios_con_categorias;
	}

	/**
	 * Deactivates a freelancer by setting their estado to 0.
	 *
	 * @param int $id       The ID of the freelancer to deactivate.
	 * @param PDO $conexion The PDO database connection object.
	 *
	 * @return bool True on success, false on failure or if freelancer not found.
	 * @throws Exception If the ID is invalid or a database error occurs.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		if ($id <= 0) {
			throw new InvalidArgumentException("ID Freelancer invalido.");
		}
		
		try {
			$query = <<<SQL
            UPDATE freelancers SET
            	estado = 0
            WHERE
            	id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Log the error instead of echoing sensitive information
			// error_log("Database error deactivating freelancer ID {$id}: " . $e->getMessage());
			throw new Exception("Error de base de datos al desactivar el freelancer: " . $e->getMessage(), 0, $e);
		} catch (Exception $e) {
			// Catch any other potential exceptions
			throw new Exception("Error inesperado al desactivar el freelancer: " . $e->getMessage(), 0, $e);
		}
	}
	
	// --- GETTERS AND SETTERS ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getNombreCompleto(): ?string
	{
		return $this->nombre_completo;
	}
	
	public function setNombreCompleto(?string $nombreCompleto): self
	{
		$this->nombre_completo = $nombreCompleto;
		return $this;
	}
	
	public function getTipoDocumento(): ?string
	{
		return $this->tipo_documento;
	}
	
	public function setTipoDocumento(?string $tipoDocumento): self
	{
		$this->tipo_documento = $tipoDocumento;
		return $this;
	}
	
	public function getDocumentoIdentidad(): ?string
	{
		return $this->documento_identidad;
	}
	
	public function setDocumentoIdentidad(?string $documentoIdentidad): self
	{
		$this->documento_identidad = $documentoIdentidad;
		return $this;
	}
	
	public function getCorreoElectronico(): ?string
	{
		return $this->correo_electronico;
	}
	
	public function setCorreoElectronico(?string $correoElectronico): self
	{
		$this->correo_electronico = $correoElectronico;
		return $this;
	}
	
	public function getNumeroTelefono(): ?string
	{
		return $this->numero_telefono;
	}
	
	public function setNumeroTelefono(?string $numeroTelefono): self
	{
		$this->numero_telefono = $numeroTelefono;
		return $this;
	}
	
	public function getDireccionCompleta(): ?string
	{
		return $this->direccion_completa;
	}
	
	public function setDireccionCompleta(?string $direccionCompleta): self
	{
		$this->direccion_completa = $direccionCompleta;
		return $this;
	}
	
	public function getCiudadResidencia(): ?string
	{
		return $this->ciudad_residencia;
	}
	
	public function setCiudadResidencia(?string $ciudadResidencia): self
	{
		$this->ciudad_residencia = $ciudadResidencia;
		return $this;
	}
	
	public function getPaisResidencia(): ?string
	{
		return $this->pais_residencia;
	}
	
	public function setPaisResidencia(?string $paisResidencia): self
	{
		$this->pais_residencia = $paisResidencia;
		return $this;
	}
	

	
	public function getFechaCreacion(): ?string
	{
		return $this->fecha_creacion;
	}
	
	public function setFechaCreacion(?string $fechaCreacion): self
	{
		$this->fecha_creacion = $fechaCreacion;
		return $this;
	}
	
	public function getLeyoConsentimiento(): ?int
	{
		return $this->leyo_consentimiento;
	}
	
	public function setLeyoConsentimiento(?int $leyoConsentimiento): self
	{
		$this->leyo_consentimiento = $leyoConsentimiento;
		return $this;
	}
	
	public function getFechaLeyeConsentimiento(): ?string
	{
		return $this->fecha_leyo_consentimiento;
	}
	
	public function setFechaLeyeConsentimiento(?string $fechaLeyeConsentimiento): self
	{
		$this->fecha_leyo_consentimiento = $fechaLeyeConsentimiento;
		return $this;
	}
	
	public function getEstado(): ?int
	{
		return $this->estado;
	}
	
	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}
	
	public function getEspecializaciones(): ?array // Getter para array
	{
		return $this->especializaciones;
	}
	
	/**
	 * Establece la lista de objetos FreelancerEspecializacion.
	 *
	 * @param FreelancerEspecializacion[] $especializaciones Array de objetos FreelancerEspecializacion.
	 *
	 * @return self
	 * @throws InvalidArgumentException Si el array contiene elementos que no son FreelancerEspecializacion.
	 */
	public function setEspecializaciones(array $especializaciones): self
	{
		foreach ($especializaciones as $esp) {
			if (!$esp instanceof FreelancerEspecializacion) {
				throw new InvalidArgumentException("El array de especializaciones solo debe contener objetos FreelancerEspecializacion.");
			}
			$esp->setFreelancer($this); // Asegurar asociación al setear
		}
		$this->especializaciones = $especializaciones;
		return $this;
	}
	
	public function getFechaLeyoConsentimiento(): ?string
	{
		return $this->fecha_leyo_consentimiento;
	}
	
	public function setFechaLeyoConsentimiento(?string $fecha_leyo_consentimiento): self
	{
		$this->fecha_leyo_consentimiento = $fecha_leyo_consentimiento;
		return $this;
	}
	
	public function getExperiencia(): ?FreelancerExperiencia
	{
		return $this->experiencia;
	}
	
	public function setExperiencia(?FreelancerExperiencia $experiencia): self
	{
		$this->experiencia = $experiencia;
		return $this;
	}
	
	public function getDisponibilidad(): ?FreelancerDisponibilidad
	{
		return $this->disponibilidad;
	}
	
	public function setDisponibilidad(?FreelancerDisponibilidad $disponibilidad): self
	{
		$this->disponibilidad = $disponibilidad;
		return $this;
	}
	
	public function getReferencias(): ?FreelancerReferencia
	{
		return $this->referencias;
	}
	
	public function setReferencias(?FreelancerReferencia $referencias): self
	{
		// Asegurar que las referencias estén asociadas a este freelancer si se setean
		if ($referencias instanceof FreelancerReferencia) {
			$referencias->setFreelancer($this);
		}
		$this->referencias = $referencias;
		return $this;
	}
	
	public function getTarifa(): ?FreelancerTarifa
	{
		return $this->tarifa;
	}
	
	public function setTarifa(?FreelancerTarifa $tarifa): self
	{
		// Asegurar que la tarifa esté asociada a este freelancer si se setea
		if ($tarifa instanceof FreelancerTarifa) {
			$tarifa->setFreelancer($this);
		}
		$this->tarifa = $tarifa;
		return $this;
	}
	
	public function getDocumentos(): ?FreelancerDocumento
	{
		return $this->documentos;
	}
	
	public function setDocumentos(?FreelancerDocumento $documentos): self
	{
		// Asegurar que los documentos estén asociados a este freelancer si se setean
		if ($documentos instanceof FreelancerDocumento) {
			$documentos->setFreelancer($this);
		}
		$this->documentos = $documentos;
		return $this;
	}
}

