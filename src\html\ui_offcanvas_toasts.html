@@include('./partial/head.html', {
	"title": "Offcanvas & Toasts", 
	"css": [
	]}
)
	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed@@if(context.theme == 'material' || context.theme == 'google') { app-with-wide-sidebar}@@if(context.theme == 'google') { app-with-light-sidebar}">
		@@include('./partial/header.html')
		@@include('./partial/sidebar.html', {"uiElementsClass": "active", "uiOffcanvasToastsClass": "active"})
		
		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN breadcrumb -->
			<ol class="breadcrumb@@if(context.theme != 'facebook'){ float-xl-end}">
				<li class="breadcrumb-item"><a href="javascript:;">Home</a></li>
				<li class="breadcrumb-item"><a href="javascript:;">UI Elements</a></li>
				<li class="breadcrumb-item active">Offcanvas & Toasts</li>
			</ol>
			<!-- END breadcrumb -->
			<!-- BEGIN page-header -->
			<h1 class="page-header">Offcanvas & Toasts <small>header small text goes here...</small></h1>
			<!-- END page-header -->
            
			<!-- BEGIN row -->
			<div class="row">
				<!-- BEGIN col-6 -->
				<div class="col-xl-6">
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="offcanvas-1">
						<div class="panel-heading">
							<h4 class="panel-title">Offcanvas</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<p>
								Below is an offcanvas example that is shown by default (via <code>.show</code> on <code>.offcanvas</code>). 
								Offcanvas includes support for a header with a close button and an optional body class for some initial <code>padding</code>.
							</p>
							<div class="row">
								<div class="col-lg-6">
									<div class="form-label">Default</div>
									<div>
										<button type="button" class="btn btn-primary" data-bs-toggle="offcanvas" href="#offcanvasStartExample">
											<i class="far fa-hand-point-up fa-fw mx-1 opacity-5"></i> Offcanvas Demo
										</button>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="form-label">Placement</div>
									<div class="btn-group">
										<a class="btn btn-default" data-bs-toggle="offcanvas" href="#offcanvasEndExample">
											<i class="fa fa-arrow-right fa-fw mx-1 opacity-5"></i> Right
										</a>
										<a class="btn btn-default" data-bs-toggle="offcanvas" href="#offcanvasBottomExample">
											<i class="fa fa-arrow-down fa-fw mx-1 opacity-5"></i> Bottom
										</a>
									</div>
								</div>
							</div>
						</div>
						<div class="panel-body bg-gray-500">
							<div class="offcanvas offcanvas-start position-static visible" id="offcanvas" data-bs-backdrop="false" data-bs-scroll="true" style="transform: none">
								<div class="offcanvas-header">
									<h5 class="offcanvas-title" id="offcanvasLabel">Offcanvas</h5>
									<button type="button" class="btn-close text-reset"></button>
								</div>
								<div class="offcanvas-body">
									Content for the offcanvas goes here. You can place just about any Bootstrap component or custom elements here.
								</div>
							</div>
						</div>
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;!-- toggler --&gt;
&lt;a class="btn btn-primary" data-bs-toggle="offcanvas" href="#offcanvasExample"&gt;
  Toggle
&lt;/a&gt;

&lt;!-- html --&gt;
&lt;div class="offcanvas offcanvas-start" id="offcanvasExample" data-bs-backdrop="false" data-bs-scroll="true"&gt;
  &lt;div class="offcanvas-header"&gt;
    &lt;h5 class="offcanvas-title" id="offcanvasLabel"&gt;Offcanvas&lt;/h5&gt;
    &lt;button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"&gt;&lt;/button&gt;
  &lt;/div&gt;
  &lt;div class="offcanvas-body"&gt;
    ...
  &lt;/div&gt;
&lt;/div&gt;

&lt;!-- placement end --&gt;
&lt;div class="offcanvas offcanvas-end"&gt;
  ...
&lt;/div&gt;

&lt;!-- placement bottom --&gt;
&lt;div class="offcanvas offcanvas-bottom"&gt;
  ...
&lt;/div&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
				</div>
				<!-- END col-6 -->
				<!-- BEGIN col-6 -->
				<div class="col-xl-6">
					<!-- BEGIN panel -->
					<div class="panel panel-inverse" data-sortable-id="offcanvas-2">
						<div class="panel-heading">
							<h4 class="panel-title">Toasts</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-success" data-toggle="panel-reload"><i class="fa fa-redo"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-danger" data-toggle="panel-remove"><i class="fa fa-times"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<p>
								To encourage extensible and predictable toasts, we recommend a header and body. 
								Toast headers use <code>display: flex</code>, allowing easy alignment of content thanks to our margin and flexbox utilities.
							</p>
							<div class="form-label">Default</div>
							<div>
								<button type="button" class="btn btn-primary" id="liveToastBtn">Show live toast</button>
							</div>
						</div>
						<div class="panel-body bg-gray-500">
							<div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
								<div class="toast-header">
									<div class="bg-blue rounded w-25px h-25px d-flex align-items-center justify-content-center text-white">
										<i class="fa fa-bell"></i>
									</div>
									<strong class="me-auto ms-2">Bootstrap</strong>
									<small>11 mins ago</small>
									<button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
								</div>
								<div class="toast-body">
									Hello, world! This is a toast message.
								</div>
							</div>
						</div>
						<!-- BEGIN hljs-wrapper -->
						<div class="hljs-wrapper">
							<pre><code class="html">&lt;!-- toggler --&gt;
&lt;button type="button" class="btn btn-primary" id="liveToastBtn"&gt;Show live toast&lt;/button&gt;

&lt;!-- html --&gt;
&lt;div class="toast show" role="alert" aria-live="assertive" aria-atomic="true"&gt;
  &lt;div class="toast-header"&gt;
    &lt;div class="bg-blue rounded w-25px h-25px d-flex align-items-center justify-content-center text-white"&gt;
      &lt;i class="fa fa-bell"&gt;&lt;/i&gt;
    &lt;/div&gt;
    &lt;strong class="me-auto ms-2"&gt;Bootstrap&lt;/strong&gt;
    &lt;small&gt;11 mins ago&lt;/small&gt;
    &lt;button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"&gt;&lt;/button&gt;
  &lt;/div&gt;
  &lt;div class="toast-body"&gt;
    Hello, world! This is a toast message.
  &lt;/div&gt;
&lt;/div&gt;

&lt;script&gt;
  $(document).on('click','#liveToastBtn', function(e) {
    e.preventDefault();
    
    $('#liveToast').toast('show');
    
    $(this).blur();
  });
&lt;/script&gt;</code></pre>
						</div>
						<!-- END hljs-wrapper -->
					</div>
					<!-- END panel -->
				</div>
				<!-- END col-6 -->
			</div>
			<!-- END row -->
		</div>
		<!-- END #content -->
		
		
		<div class="offcanvas offcanvas-start" id="offcanvasStartExample">
			<div class="offcanvas-header">
				<h5 class="offcanvas-title">Offcanvas</h5>
				<button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
			</div>
			<div class="offcanvas-body">
				<div>
					Some text as placeholder. In real life you can have the elements you have chosen. Like, text, images, lists, etc.
				</div>
				<div class="dropdown mt-3">
					<button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
						Dropdown button
					</button>
					<ul class="dropdown-menu">
						<li><a class="dropdown-item" href="#">Action</a></li>
						<li><a class="dropdown-item" href="#">Another action</a></li>
						<li><a class="dropdown-item" href="#">Something else here</a></li>
					</ul>
				</div>
			</div>
		</div>
		<div class="offcanvas offcanvas-end" id="offcanvasEndExample">
			<div class="offcanvas-header">
				<h5 class="offcanvas-title">Offcanvas</h5>
				<button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
			</div>
			<div class="offcanvas-body">
				<div>
					Some text as placeholder. In real life you can have the elements you have chosen. Like, text, images, lists, etc.
				</div>
				<div class="dropdown mt-3">
					<button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
						Dropdown button
					</button>
					<ul class="dropdown-menu">
						<li><a class="dropdown-item" href="#">Action</a></li>
						<li><a class="dropdown-item" href="#">Another action</a></li>
						<li><a class="dropdown-item" href="#">Something else here</a></li>
					</ul>
				</div>
			</div>
		</div>
		<div class="offcanvas offcanvas-bottom" id="offcanvasBottomExample">
			<div class="offcanvas-header">
				<h5 class="offcanvas-title">Offcanvas</h5>
				<button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"></button>
			</div>
			<div class="offcanvas-body">
				<div>
					Some text as placeholder. In real life you can have the elements you have chosen. Like, text, images, lists, etc.
				</div>
				<div class="dropdown mt-3">
					<button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
						Dropdown button
					</button>
					<ul class="dropdown-menu">
						<li><a class="dropdown-item" href="#">Action</a></li>
						<li><a class="dropdown-item" href="#">Another action</a></li>
						<li><a class="dropdown-item" href="#">Something else here</a></li>
					</ul>
				</div>
			</div>
		</div>

		<div class="toast-container">
			<div id="liveToast" class="toast hide">
				<div class="toast-header">
					<div class="bg-blue rounded w-25px h-25px d-flex align-items-center justify-content-center text-white">
						<i class="fa fa-bell"></i>
					</div>
					<strong class="me-auto ms-2">Notification</strong>
					<small>11 mins ago</small>
					<button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
				<div class="toast-body">
					Hello, world! This is a toast message.
				</div>
			</div>
		</div>
		
		@@include('./partial/theme-panel.html')
		@@include('./partial/scroll-top-btn.html')
	</div>
	<!-- END #app -->
	
@@include('./partial/script.html', {
	"script": [
		"../assets/plugins/@highlightjs/cdn-assets/highlight.min.js",
		"../assets/js/demo/render.highlight.js"
	]}
)

<script>
	$(document).on('click','#liveToastBtn', function(e) {
		e.preventDefault();
		
		$('#liveToast').toast('show');
		
		$(this).blur();
	});
</script>