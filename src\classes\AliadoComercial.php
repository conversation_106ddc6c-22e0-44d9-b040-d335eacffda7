<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;
use App\classes\ClientePotencial;
use App\classes\Servicio;
use App\classes\ServicioCategoria;

/**
 * Represents a commercial ally (Aliado Comercial).
 */
class AliadoComercial
{
    // --- Attributes corresponding to DB columns ---
private ?int    $id                           = null;
private ?string $nombre_razon_social          = null;
private ?string $cedula_nit                   = null;
private ?string $telefono_contacto            = null;
private ?string $correo_electronico           = null;
private ?string $ciudad_operacion             = null;
private ?string $pais_operacion               = null;
private ?string $tipo_alianza                 = null;
private ?string $fecha_registro               = null;
private ?int    $estado                       = null;  // 1 = Active, 0 = Inactive
private ?int    $acepta_declaracion_veracidad = null;  // 1 = Yes, 0 = No
private ?int    $acepta_terminos_condiciones  = null;  // 1 = Yes, 0 = No
private ?int    $viable                       = null;  // 1 = Yes, 0 = No
private ?ClientePotencial $cliente_potencial  = null;

    /**
     * Constructor: Initializes properties with default values.
     */
    public function __construct()
    {
        $this->id                           = null;  // Use null for ID before insertion
        $this->nombre_razon_social          = null;
        $this->cedula_nit                   = null;
        $this->telefono_contacto            = null;
        $this->correo_electronico           = null;
        $this->ciudad_operacion             = null;
        $this->pais_operacion               = null;
        $this->tipo_alianza                 = null;
        $this->fecha_registro               = null;  // Usually set by DB default
        $this->estado                       = 1;     // Default to active
        $this->acepta_declaracion_veracidad = 0;     // Default to not accepted
        $this->acepta_terminos_condiciones  = 0;     // Default to not accepted
        $this->viable                       = 0;     // Default to not viable
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     *
     * @param array $resultado Associative array containing property values.
     * @return self Returns a new instance of AliadoComercial.
     * @throws Exception If there's an error during construction.
     */
public static function construct(array $resultado = []): self
{
    try {
        $objeto                               = new self();
        $objeto->id                           = isset($resultado['id']) ? (int)$resultado['id'] : null;
        $objeto->nombre_razon_social          = $resultado['nombre_razon_social'] ?? null;
        $objeto->cedula_nit                   = $resultado['cedula_nit'] ?? null;
        $objeto->telefono_contacto            = $resultado['telefono_contacto'] ?? null;
        $objeto->correo_electronico           = $resultado['correo_electronico'] ?? null;
        $objeto->ciudad_operacion             = $resultado['ciudad_operacion'] ?? null;
        $objeto->pais_operacion               = $resultado['pais_operacion'] ?? null;
        $objeto->tipo_alianza                 = $resultado['tipo_alianza'] ?? null;
        $objeto->fecha_registro               = $resultado['fecha_registro'] ?? null;
        $objeto->estado                       = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;                                              // Default to active (1) if not set
        $objeto->acepta_declaracion_veracidad = isset($resultado['acepta_declaracion_veracidad']) ? (int)$resultado['acepta_declaracion_veracidad'] : 0;  // Default to 0 if not set
        $objeto->acepta_terminos_condiciones  = isset($resultado['acepta_terminos_condiciones']) ? (int)$resultado['acepta_terminos_condiciones'] : 0;    // Default to 0 if not set
        $objeto->viable                       = isset($resultado['viable']) ? (int)$resultado['viable'] : 0;                                              // Default to 0 if not set
        $objeto->cliente_potencial            = new ClientePotencial();
        $objeto->cliente_potencial->setNombreEmpresa($resultado['nombre_empresa'] ?? null);

        return $objeto;

    } catch (Exception $e) {
        // Consider logging the error here
        throw new Exception("Error constructing AliadoComercial: " . $e->getMessage());
    }
}

    /**
     * Retrieves an AliadoComercial object from the database by its ID.
     *
     * @param int $id       The ID of the AliadoComercial to retrieve.
     * @param PDO $conexion The database connection object.
     * @return self|null An AliadoComercial object if found, null otherwise.
     * @throws Exception If there is an error during the database query or object construction.
     */
public static function get(int $id, PDO $conexion): ?self
{
    if ($id <= 0) {
        throw new InvalidArgumentException("Invalid ID provided.");
    }

    try {
        $query = <<<SQL
        SELECT ac.*, cp.nombre_empresa
        FROM aliados_comerciales ac
        INNER JOIN clientes_potenciales cp ON ac.id = cp.aliado_comercial_id
        WHERE ac.id = :id
        LIMIT 1
        SQL;

        $statement = $conexion->prepare($query);
        $statement->bindValue(":id", $id, PDO::PARAM_INT);
        $statement->execute();
        $resultado = $statement->fetch(PDO::FETCH_ASSOC);

        return $resultado ? self::construct($resultado) : null;

    } catch (PDOException $e) {
        error_log("Database error getting AliadoComercial (ID: $id): " . $e->getMessage());
        throw new Exception("Database error fetching Aliado Comercial: " . $e->getMessage());
    } catch (Exception $e) {
        error_log("Error getting AliadoComercial (ID: $id): " . $e->getMessage());
        throw new Exception("Error fetching Aliado Comercial: " . $e->getMessage());
    }
}

    /**
     * Retrieves a list of all AliadoComercial objects from the database.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of AliadoComercial objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
public static function get_list(array $parametros, PDO $conexion): array
{
    try {
        $viable = $parametros['viable'] ?? null;

        // Fetch only active records (estado = 1)
        $query = <<<SQL
        SELECT ac.*, cp.nombre_empresa
        FROM aliados_comerciales ac
        INNER JOIN clientes_potenciales cp ON ac.id = cp.aliado_comercial_id
        WHERE 
            ac.estado = 1
            AND (:viable_empty IS NULL OR ac.viable = :viable)
        ORDER BY ac.nombre_razon_social
        SQL;

        $statement = $conexion->prepare($query);
        $statement->bindValue(":viable_empty", $viable);
        $statement->bindValue(":viable", $viable);
        $statement->execute();
        $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

        $listado = [];
        if ($resultados) {
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
        }
        return $listado;

    } catch (PDOException $e) {
        error_log("Database error getting AliadoComercial list: " . $e->getMessage());
        throw new Exception("Database error fetching Aliado Comercial list: " . $e->getMessage());
    } catch (Exception $e) {
        error_log("Error getting AliadoComercial list: " . $e->getMessage());
        throw new Exception("Error fetching Aliado Comercial list: " . $e->getMessage());
    }
}

    /**
     * Saves (inserts or updates) the current AliadoComercial instance to the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        $this->validarDatos(); // Validate data before saving

        try {
            if ($this->getId() !== null && $this->getId() > 0) {
                // ID exists, perform an update
                return $this->_update($conexion);
            } else {
                // No ID or ID is 0/null, perform an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $infoParaError = $this->getNombreRazonSocial() ?? $this->getCorreoElectronico() ?? $this->getCedulaNit() ?? '[desconocido]';
            if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Handle unique constraint violation
                throw new Exception("Error saving Aliado Comercial '$infoParaError': Cédula/NIT or Correo Electrónico already exists.");
            } else {
                error_log("Database error saving AliadoComercial '$infoParaError': " . $e->getMessage());
                throw new Exception("Database error saving Aliado Comercial: " . $e->getMessage());
            }
        } catch (Exception $e) {
            error_log("General error saving AliadoComercial: " . $e->getMessage());
            throw new Exception("Error saving Aliado Comercial: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current AliadoComercial instance into the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     */
    private function _insert(PDO $conexion): bool
    {
        $query = <<<SQL
        INSERT INTO aliados_comerciales (
             nombre_razon_social
            ,cedula_nit
            ,telefono_contacto
            ,correo_electronico
            ,ciudad_operacion
            ,pais_operacion
            ,tipo_alianza
            ,acepta_declaracion_veracidad
            ,acepta_terminos_condiciones
            ,viable
            ,fecha_registro
        ) VALUES (
             :nombre_razon_social
            ,:cedula_nit
            ,:telefono_contacto
            ,:correo_electronico
            ,:ciudad_operacion
            ,:pais_operacion
            ,:tipo_alianza
            ,:acepta_declaracion_veracidad
            ,:acepta_terminos_condiciones
            ,:viable
            ,:fecha_registro
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':nombre_razon_social', $this->getNombreRazonSocial(), PDO::PARAM_STR);
        $statement->bindValue(':cedula_nit', $this->getCedulaNit(), PDO::PARAM_STR);
        $statement->bindValue(':telefono_contacto', $this->getTelefonoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':correo_electronico', $this->getCorreoElectronico(), PDO::PARAM_STR);
        $statement->bindValue(':ciudad_operacion', $this->getCiudadOperacion(), PDO::PARAM_STR);
        $statement->bindValue(':pais_operacion', $this->getPaisOperacion(), PDO::PARAM_STR);
        $statement->bindValue(':tipo_alianza', $this->getTipoAlianza(), PDO::PARAM_STR);
        $statement->bindValue(':acepta_declaracion_veracidad', $this->getAceptaDeclaracionVeracidad(), PDO::PARAM_INT);
        $statement->bindValue(':acepta_terminos_condiciones', $this->getAceptaTerminosCondiciones(), PDO::PARAM_INT);
        $statement->bindValue(':viable', $this->getViable(), PDO::PARAM_INT);
        $statement->bindValue(':fecha_registro', create_datetime());

        $success = $statement->execute();

        if ($success) {
            $this->setId((int)$conexion->lastInsertId());
            // Optionally fetch and set fecha_registro if needed immediately
            // $this->fecha_registro = self::get($this->id, $conexion)?->getFechaRegistro();
        } else {
            error_log("Failed to insert AliadoComercial: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current AliadoComercial instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     */
    private function _update(PDO $conexion): bool
    {
        $query = <<<SQL
        UPDATE aliados_comerciales SET
             nombre_razon_social = :nombre_razon_social
            ,cedula_nit = :cedula_nit
            ,telefono_contacto = :telefono_contacto
            ,correo_electronico = :correo_electronico
            ,ciudad_operacion = :ciudad_operacion
            ,pais_operacion = :pais_operacion
            ,tipo_alianza = :tipo_alianza
            ,acepta_declaracion_veracidad = :acepta_declaracion_veracidad
            ,acepta_terminos_condiciones = :acepta_terminos_condiciones
            ,viable = :viable
            -- fecha_registro is usually not updated
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':nombre_razon_social', $this->getNombreRazonSocial(), PDO::PARAM_STR);
        $statement->bindValue(':cedula_nit', $this->getCedulaNit(), PDO::PARAM_STR);
        $statement->bindValue(':telefono_contacto', $this->getTelefonoContacto(), PDO::PARAM_STR);
        $statement->bindValue(':correo_electronico', $this->getCorreoElectronico(), PDO::PARAM_STR);
        $statement->bindValue(':ciudad_operacion', $this->getCiudadOperacion(), PDO::PARAM_STR);
        $statement->bindValue(':pais_operacion', $this->getPaisOperacion(), PDO::PARAM_STR);
        $statement->bindValue(':tipo_alianza', $this->getTipoAlianza(), PDO::PARAM_STR);
        $statement->bindValue(':acepta_declaracion_veracidad', $this->getAceptaDeclaracionVeracidad(), PDO::PARAM_INT);
        $statement->bindValue(':acepta_terminos_condiciones', $this->getAceptaTerminosCondiciones(), PDO::PARAM_INT);
        $statement->bindValue(':viable', $this->getViable(), PDO::PARAM_INT);
        $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update AliadoComercial (ID: {$this->getId()}): " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deactivates an AliadoComercial record in the database by setting its estado to 0.
     *
     * @param int $id       The ID of the AliadoComercial to deactivate.
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If the ID is invalid or a database error occurs.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        if ($id <= 0) {
            throw new InvalidArgumentException("Invalid ID provided for deactivation.");
        }

        try {
            $query = "UPDATE aliados_comerciales SET estado = 0 WHERE id = :id";
            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            return $statement->execute();
        } catch (PDOException $e) {
            error_log("Database error deactivating AliadoComercial (ID: $id): " . $e->getMessage());
            throw new Exception("Database error deactivating Aliado Comercial: " . $e->getMessage());
        }
    }

    /**
     * Marks the current AliadoComercial instance as viable (viable = 1) in the database.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If the instance doesn't have a valid ID or a database error occurs.
     */
    public function marcarComoViable(PDO $conexion): bool
    {
        $id = $this->getId();
        if ($id === null || $id <= 0) {
            throw new Exception("Cannot mark as viable: AliadoComercial instance does not have a valid ID.");
        }

        try {
            $query = "UPDATE aliados_comerciales SET viable = 1 WHERE id = :id";
            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);
            return $statement->execute();
        } catch (PDOException $e) {
            error_log("Database error marking AliadoComercial as viable (ID: $id): " . $e->getMessage());
            throw new Exception("Database error marking Aliado Comercial as viable: " . $e->getMessage());
        }
    }


    /**
     * Validates the essential data for the AliadoComercial.
     * Throws an Exception if validation fails.
     *
     * @throws Exception If validation fails.
     */
    private function validarDatos(): void
    {
        if (empty(trim((string)$this->getNombreRazonSocial()))) {
            throw new Exception("El nombre o razón social es requerido.");
        }
        if (empty(trim((string)$this->getCedulaNit()))) {
            throw new Exception("La cédula o NIT es requerido.");
        }
        if (empty(trim((string)$this->getTelefonoContacto()))) {
            throw new Exception("El teléfono de contacto es requerido.");
        }
        if (empty(trim((string)$this->getCorreoElectronico()))) {
            throw new Exception("El correo electrónico es requerido.");
        }
        if (!filter_var($this->getCorreoElectronico(), FILTER_VALIDATE_EMAIL)) {
            throw new Exception("El formato del correo electrónico no es válido.");
        }
        if (empty(trim((string)$this->getCiudadOperacion()))) {
            throw new Exception("La ciudad de operación es requerida.");
        }
        if (empty(trim((string)$this->getPaisOperacion()))) {
            throw new Exception("El país de operación es requerido.");
        }
        if (empty(trim((string)$this->getTipoAlianza()))) {
            throw new Exception("El tipo de alianza es requerido.");
        }
        // Validate acceptance checkboxes (must be 1)
        if ($this->getAceptaDeclaracionVeracidad() !== 1) {
            throw new Exception("Debe aceptar la declaración de veracidad.");
        }
        if ($this->getAceptaTerminosCondiciones() !== 1) {
            throw new Exception("Debe aceptar los términos y condiciones.");
        }

    }


    // --- GETTERS AND SETTERS ---

public function getId(): ?int
{
    return $this->id;
}

public function getClientePotencial(): ?ClientePotencial
{
    return $this->cliente_potencial;
}

public function setClientePotencial(?ClientePotencial $cliente_potencial): self
{
    $this->cliente_potencial = $cliente_potencial;
    return $this;
}

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombreRazonSocial(): ?string
    {
        return $this->nombre_razon_social;
    }

    public function setNombreRazonSocial(?string $nombre_razon_social): self
    {
        $this->nombre_razon_social = $nombre_razon_social;
        return $this;
    }

    public function getCedulaNit(): ?string
    {
        return $this->cedula_nit;
    }

    public function setCedulaNit(?string $cedula_nit): self
    {
        $this->cedula_nit = $cedula_nit;
        return $this;
    }

    public function getTelefonoContacto(): ?string
    {
        return $this->telefono_contacto;
    }

    public function setTelefonoContacto(?string $telefono_contacto): self
    {
        $this->telefono_contacto = $telefono_contacto;
        return $this;
    }

    public function getCorreoElectronico(): ?string
    {
        return $this->correo_electronico;
    }

    public function setCorreoElectronico(?string $correo_electronico): self
    {
        $this->correo_electronico = $correo_electronico;
        return $this;
    }

    public function getCiudadOperacion(): ?string
    {
        return $this->ciudad_operacion;
    }

    public function setCiudadOperacion(?string $ciudad_operacion): self
    {
        $this->ciudad_operacion = $ciudad_operacion;
        return $this;
    }

    public function getPaisOperacion(): ?string
    {
        return $this->pais_operacion;
    }

    public function setPaisOperacion(?string $pais_operacion): self
    {
        $this->pais_operacion = $pais_operacion;
        return $this;
    }



    public function getTipoAlianza(): ?string
    {
        return $this->tipo_alianza;
    }

    public function setTipoAlianza(?string $tipo_alianza): self
    {
        $this->tipo_alianza = $tipo_alianza;
        return $this;
    }

    public function getFechaRegistro(): ?string
    {
        return $this->fecha_registro;
    }

    /**
     * Note: Setting fecha_registro might not be typical as it's often handled by the database.
     */
    public function setFechaRegistro(?string $fecha_registro): self
    {
        $this->fecha_registro = $fecha_registro;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        // Optional: Add validation if needed (e.g., ensure estado is 0 or 1)
        $this->estado = $estado;
        return $this;
    }

    public function getAceptaDeclaracionVeracidad(): ?int
    {
        return $this->acepta_declaracion_veracidad;
    }

    public function setAceptaDeclaracionVeracidad(?int $acepta_declaracion_veracidad): self
    {
        $this->acepta_declaracion_veracidad = ($acepta_declaracion_veracidad === 1) ? 1 : 0; // Ensure it's 0 or 1
        return $this;
    }

    public function getAceptaTerminosCondiciones(): ?int
    {
        return $this->acepta_terminos_condiciones;
    }

    public function setAceptaTerminosCondiciones(?int $acepta_terminos_condiciones): self
    {
        $this->acepta_terminos_condiciones = ($acepta_terminos_condiciones === 1) ? 1 : 0; // Ensure it's 0 or 1
        return $this;
    }

    public function getViable(): ?int
    {
        return $this->viable;
    }

    public function setViable(?int $viable): self
    {
        $this->viable = ($viable === 1) ? 1 : 0; // Ensure it's 0 or 1
        return $this;
    }

    /**
     * Retrieves the services associated with this commercial ally through its ClientePotencial.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return array Array of Servicio objects associated with this ally's ClientePotencial.
     * @throws Exception If there's an error during the database operation.
     */
    public function getServicios(PDO $conexion): array
    {
        if ($this->getId() === null || $this->getId() <= 0) {
            return [];
        }

        try {
            // Get the ClientePotencial associated with this AliadoComercial
            $clientePotencial = ClientePotencial::getByAliadoId($this->getId(), $conexion);

            if (!$clientePotencial) {
                return [];
            }

            // Get service IDs from ClientePotencial
            $servicios_ids = $clientePotencial->getServicios($conexion);

            if (empty($servicios_ids)) {
                return [];
            }

            // Get full Servicio objects with their categories
            $servicios = [];
            foreach ($servicios_ids as $servicio_id) {
                $servicio = Servicio::get($servicio_id, $conexion);
                if ($servicio) {
                    $servicios[] = $servicio;
                }
            }

            return $servicios;

        } catch (Exception $e) {
            error_log("Error getting services for AliadoComercial ID {$this->getId()}: " . $e->getMessage());
            throw new Exception("Error retrieving services for commercial ally: " . $e->getMessage());
        }
    }

    /**
     * Retrieves the services with their categories for display purposes.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return array Array of arrays containing service and category information.
     * @throws Exception If there's an error during the database operation.
     */
    public function getServiciosConCategorias(PDO $conexion): array
    {
        $servicios = $this->getServicios($conexion);
        $servicios_con_categorias = [];

        foreach ($servicios as $servicio) {
            try {
                $categoria = ServicioCategoria::get($servicio->getId_servicio_categoria(), $conexion);
                $servicios_con_categorias[] = [
                    'servicio' => $servicio,
                    'categoria' => $categoria
                ];
            } catch (Exception $e) {
                // Log error but continue with other services
                error_log("Error getting category for service ID {$servicio->getId()}: " . $e->getMessage());
                $servicios_con_categorias[] = [
                    'servicio' => $servicio,
                    'categoria' => null
                ];
            }
        }

        return $servicios_con_categorias;
    }

}
